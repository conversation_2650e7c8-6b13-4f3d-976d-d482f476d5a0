<!--
 * @Description: 培训任务列表组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-17 10:46:59
 * @LastEditTime: 2025-07-04 14:17:20
-->
<template>
  <view class="bg-white">
    <view v-show="studyList.length">
      <view
        v-for="(item, index) in studyList"
        :key="index"
        class="mb-3 p-3 border border-gray-100 rounded-lg"
      >
        <view class="flex items-center justify-between gap-2">
          <view class="flex-1 min-w-0">
            <!-- 第一行：图标、类型、名称 -->
            <view class="flex items-center gap-2">
              <text
                :class="getTypeIcon(item.type)"
                :style="{ color: getTypeColor(item.type) }"
                class="text-20px flex-shrink-0"
              ></text>
              <text class="text-14px truncate">
                <text :style="{ color: getTypeColor(item.type) }"
                  >{{ getTypePrefix(item.type) }}：</text
                >
                {{ item.name }}
                <!-- 添加选修课程标签 -->
                <text
                  v-if="item.type === 'course' && item.courseType === '1'"
                  class="ml-1 px-1.5 py-0.5 text-xs rounded bg-orange-100 text-orange-600"
                >
                  选修
                </text>
              </text>
            </view>
            <!-- 第二行：截止日期 -->
            <view class="text-12px text-gray-500 my-1">
              截止日期：{{ formatDate(item.endTime) }}
            </view>
            <!-- 进度条 -->
            <view
              v-if="item.type === 'course'"
              class="w-full h-2 bg-gray-100 rounded-full overflow-hidden"
            >
              <view
                class="h-full rounded-full"
                :style="{
                  width: (item.learningProcess || 0) * 100 + '%',
                  backgroundColor: getTypeColor(item.type)
                }"
              ></view>
            </view>
          </view>
          <!-- 按钮 -->
          <button
            class="text-12px rounded flex-shrink-0 h-32px flex items-center justify-center px-2"
            :class="[getButtonClass(item), getButtonWidthClass(item)]"
            :style="{
              backgroundColor: getButtonColor(item)
            }"
            @click="handleItemClick(item)"
          >
            {{ getButtonText(item) }}
          </button>
        </view>
      </view>
    </view>
    <!-- 空状态 -->
    <xw-empty :isShow="!studyList.length" text="暂无学习安排" textColor="#777777" />
  </view>
</template>

<script>
  import dayjs from "dayjs"
  import xwEmpty from "@/components/xw-empty/xw-empty"

  export default {
    name: "StudyArrangement",
    components: { xwEmpty },
    props: {
      studyList: {
        type: Array,
          default: () => []
      },
      currentTaskId: {
        type: String,
        default: ""
      }
    },
    methods: {
      formatDate(date) {
        return date ? dayjs(date).format("YYYY-MM-DD") : "--"
      },

      // 获取类型图标
      getTypeIcon(type) {
        const icons = {
          course: "i-material-symbols-menu-book",
          exam: "i-material-symbols-quiz",
          questionnaire: "i-material-symbols-assignment",
          resource: "i-material-symbols-folder"
        }
        return icons[type] || icons.course
      },
      getTypeColor(type) {
        const colors = {
          course: "#1e91fc",
          exam: "#25b692",
          questionnaire: "#f59a23",
          resource: "#8080ff"
        }
        return colors[type] || colors.course
      },
      // 获取类型前缀
      getTypePrefix(type) {
        const prefixes = {
          course: "在线课程",
          exam: "在线考试",
          questionnaire: "问卷调查",
          resource: "在线资料"
        }
        return prefixes[type] || "在线课程"
      },

      // 检查前置任务是否完成
      checkPreconditionCompleted(item) {
        // 如果没有前置任务，直接返回true
        if (!item.preconditionLinkList || item.preconditionLinkList.length === 0) {
          return true
        }

        // 检查所有前置任务的状态是否都为'2'（已完成）
        return item.preconditionLinkList.every(precondition =>
          precondition.preconditionStatus === '2'
        )
      },

      // 获取按钮文本
      getButtonText(item) {
        if (item.status === "completed") return "已完成"

        // 检查前置任务是否完成
        if (!this.checkPreconditionCompleted(item)) {
          return "请先完成前置任务"
        }

        const buttonTexts = {
          course: "去学习",
          exam: "去考试",
          questionnaire: "去完成",
          resource: "去完成"
        }
        return buttonTexts[item.type] || "去完成"
      },

      // 获取按钮颜色
      getButtonColor(item) {
        // 已完成状态
        if (item.status === "completed") return "#9CA3AF"

        // 前置任务未完成状态 - 使用橙色背景提示用户
        if (!this.checkPreconditionCompleted(item)) return "#FEF3C7"

        // 正常状态
        return this.getTypeColor(item.type)
      },

      // 获取按钮样式类
      getButtonClass(item) {
        // 前置任务未完成状态 - 使用橙色文字
        if (!this.checkPreconditionCompleted(item)) {
          return "text-orange-600 border border-orange-200"
        }

        // 其他状态使用白色文字
        return "text-white"
      },

      // 获取按钮宽度类
      getButtonWidthClass(item) {
        // 前置任务未完成状态需要更宽的按钮
        if (!this.checkPreconditionCompleted(item)) {
          return "min-w-120px"
        }

        // 其他状态使用固定宽度
        return "w-80px"
      },
      // 处理项目点击
      handleItemClick(item) {
        if (item.status === "completed") return

        // 检查前置任务是否完成
        if (!this.checkPreconditionCompleted(item)) {
          uni.showToast({
            title: "请先完成前置任务",
            icon: "none",
            duration: 2000
          })
          return
        }

        // 根据类型跳转到不同页面
        const urls = {
          course: `/pages/course/detail/index?id=${item.id}&courseName=${encodeURIComponent(
            item.courseName
          )}&taskId=${this.currentTaskId}`,
          exam: `/pages/mine/exam/prepare/index?baseId=${item.id}&arrangeId=${item.examArrangeId}&taskId=${this.currentTaskId}`,
          questionnaire: `/pages/mine/my-questionnaire/doQuestionnaire/index?questionnaireId=${item.questionnaireId}&questionnaireIssuedId=${item.questionnaireIssuedId}&taskId=${this.currentTaskId}`,
          resource: `/pages/home/<USER>/resource-detail/index?id=${item.manageId}&taskId=${this.currentTaskId}`
        }
        const url = urls[item.type]
        if (url) {
          uni.navigateTo({ url })
        }
      }
    }
  }
</script>
