<!--
  @Description: 游戏中心
  @Author: <PERSON>
 * @LastEditors: <PERSON>
  @Date: 2025-01-27
 * @LastEditTime: 2025-07-18 09:27:35
-->
<template>
  <div class="game-page">
    <!-- 使用GlobalHeader组件 -->
    <GlobalHeader :search-placeholder="'搜索游戏'" @search="handleSearch" />

    <!-- 游戏列表 -->
    <div class="game-list-container">
      <!-- 游戏卡片列表 -->
      <div class="game-list">
        <div
          v-for="(item, index) in gameList"
          :key="item.id"
          class="game-item"
          @click="goToGameDetail(item)"
        >
          <div class="game-cover">
            <image :src="item.gameCover" mode="aspectFill" class="cover-image" />
            <div class="play-button">
              <div class="play-icon">▶</div>
            </div>
          </div>
          <div class="game-info">
            <div class="game-name">{{ item.gameName }}</div>
            <div class="game-type">{{ item.gameType || "休闲游戏" }}</div>
            <div class="game-action">
              <button class="play-btn">开始游戏</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多提示 -->
      <div v-if="loading && gameList.length > 0" class="loading-more">
        <text>正在加载...</text>
      </div>
      <div v-else-if="!hasMore && gameList.length > 0" class="no-more">
        <text>没有更多了</text>
      </div>

      <!-- 空状态 -->
      <div v-if="gameList.length === 0 && !loading" class="empty-state">
        <text class="empty-text">暂无游戏</text>
      </div>
    </div>

    <!-- 自定义tabbar -->
    <CustomTabbar />
  </div>
</template>

<script>
  import { listGame } from "@/api/game"
  import GlobalHeader from "@/components/GlobalHeader/index.vue"
  import CustomTabbar from "@/components/custom-tab-bar/custom-tab-bar.vue"

  export default {
    name: "GameIndex",

    components: {
      GlobalHeader,
      CustomTabbar
    },
    data() {
      return {
        gameList: [],
        loading: false,
        hasMore: true,
        pageNum: 1,
        pageSize: 20,
        searchKeyword: "" // 搜索关键词
      }
    },
    onLoad() {
      this.loadGameList()
    },
    onShow() {
      // 设置状态栏颜色
      uni.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff"
      })
    },
    onPullDownRefresh() {
      this.loadGameList(true)
      uni.stopPullDownRefresh()
    },
    onReachBottom() {
      console.log("触发onReachBottom:", { hasMore: this.hasMore, loading: this.loading })
      if (this.hasMore && !this.loading) {
        console.log("开始加载更多游戏数据")
        this.loadGameList()
      }
    },
    methods: {
      async loadGameList(isRefresh = false) {
        if (this.loading) return

        this.loading = true

        try {
          const currentPageNum = isRefresh ? 1 : this.pageNum
          const params = {
            pageNum: currentPageNum,
            pageSize: this.pageSize,
            status: 1 // 只查询上架的游戏
          }

          // 如果有搜索关键词，添加到参数中
          if (this.searchKeyword) {
            params.gameName = this.searchKeyword
          }

          console.log("请求游戏列表参数:", params)
          const res = await listGame(params)
          const { rows = [], total = 0 } = res
          console.log("游戏列表响应:", { rows: rows.length, total, currentPage: currentPageNum })

          if (isRefresh) {
            this.gameList = rows
            this.pageNum = 2 // 下次请求第2页
          } else {
            this.gameList = [...this.gameList, ...rows]
            this.pageNum++ // 下次请求页码+1
          }

          // 判断是否还有更多数据
          this.hasMore = this.gameList.length < total
          console.log("分页状态:", {
            currentTotal: this.gameList.length,
            serverTotal: total,
            hasMore: this.hasMore,
            nextPageNum: this.pageNum
          })
        } catch (error) {
          console.error("获取游戏列表失败:", error)
          uni.showToast({
            title: "获取游戏列表失败",
            icon: "none"
          })
        } finally {
          this.loading = false
        }
      },

      // 搜索游戏
      handleSearch(keyword) {
        this.searchKeyword = keyword
        this.pageNum = 1
        this.hasMore = true
        this.loadGameList(true)
      },

      // 跳转到游戏详情
      goToGameDetail(gameItem) {
        uni.navigateTo({
          url: `/pages/game/detail/index?id=${gameItem.id}`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .game-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding-bottom: calc(100rpx + env(safe-area-inset-bottom) + 20rpx);
  }

  .game-list-container {
    padding: 16px;
  }

  .game-list {
    padding: 16px 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .game-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.95);
    }
  }

  .game-cover {
    position: relative;
    width: 100%;
    height: 120px;

    .cover-image {
      width: 100%;
      height: 100%;
    }

    .play-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .play-icon {
        color: white;
        font-size: 16px;
        margin-left: 2px;
      }
    }
  }

  .game-info {
    padding: 12px;

    .game-name {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .game-type {
      font-size: 12px;
      color: #666;
      margin-bottom: 12px;
      padding: 2px 8px;
      background: #f0f9ff;
      border-radius: 8px;
      display: inline-block;
    }

    .game-action {
      .play-btn {
        width: 100%;
        height: 32px;
        background: #eff4fe;
        color: #666;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;

        &:active {
          background: #409eff;
          color: white;
        }
      }
    }
  }

  .loading-more,
  .no-more {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;

    .empty-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
    }
  }

  /* 适配不同屏幕尺寸 */
  @media (max-width: 375px) {
    .game-list {
      gap: 12px;
    }

    .game-item .game-cover {
      height: 100px;
    }
  }
</style>
