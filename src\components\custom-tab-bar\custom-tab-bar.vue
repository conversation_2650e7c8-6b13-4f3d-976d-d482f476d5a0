<template>
  <view class="tabbar" :style="{ zIndex: 999, paddingBottom: safeAreaBottom }">
    <view
      v-for="(item, index) in tabList"
      :key="index"
      class="tab-item"
      :class="{ active: isActive(item) }"
      @click="switchTab(item)"
    >
      <image :src="isActive(item) ? item.selectedIconPath : item.iconPath" />
      <text>{{ item.text }}</text>
    </view>
  </view>
</template>

<script>
  import { mapGetters } from "vuex"

  // 基础TAB配置
  const BASE_TAB_CONFIG = [
    {
      pagePath: "/pages/index",
      iconPath: "/static/images/tabbar/find.png",
      selectedIconPath: "/static/images/tabbar/find_active.png",
      text: "发现"
    },
    {
      pagePath: "/pages/course/index",
      iconPath: "/static/images/tabbar/course.png",
      selectedIconPath: "/static/images/tabbar/course_active.png",
      text: "课程"
    },
    {
      pagePath: "/pages/learn/index",
      iconPath: "/static/images/tabbar/study.png",
      selectedIconPath: "/static/images/tabbar/study_active.png",
      text: "学习"
    },
    {
      pagePath: "/pages/game/index",
      iconPath: "/static/images/tabbar/game.png",
      selectedIconPath: "/static/images/tabbar/game_active.png",
      text: "游戏"
    },
    {
      pagePath: "/pages/mine/index",
      iconPath: "/static/images/tabbar/mine.png",
      selectedIconPath: "/static/images/tabbar/mine_active.png",
      text: "我的"
    }
  ]

  // 租户特定配置（基于基础配置的修改）
  const TENANT_TAB_MODIFICATIONS = {
    // pk租户隐藏游戏菜单
    pk: {
      excludePaths: ["/pages/game/index"]
    }
  }

  export default {
    data() {
      return {
        tabList: [],
        safeAreaBottom: "60", // 安全区域高度
        currentPath: ""
      }
    },
    
    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },

    mounted() {
      this.initTabBar()
      this.calcSafeArea()
      this.updateCurrentPath()
    },

    watch: {
      // 监听domainName变化，重新初始化tabbar
      domainName(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.initTabBar()
        }
      }
    },

    methods: {
      // 统一激活状态判断
      isActive(item) {
        const currentPath = this.normalizePath(this.currentPath)
        const itemPath = this.normalizePath(item.pagePath)
        return currentPath === itemPath
      },

      // 路径格式化方法
      normalizePath(path) {
        if (!path) return ""
        return path.startsWith("/") ? path : `/${path}`
      },

      // 更新当前路径
      updateCurrentPath() {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        if (currentPage) {
          this.currentPath = currentPage.route
        }
      },

      // 生成特定租户的TAB配置
      generateTabConfig(domainName) {
        let config = [...BASE_TAB_CONFIG]
        
        // 如果没有domainName，展示所有tabbar
        if (!domainName) {
          return config
        }
        
        const modification = TENANT_TAB_MODIFICATIONS[domainName]
        if (modification) {
          // 排除指定路径
          if (modification.excludePaths) {
            config = config.filter(item => !modification.excludePaths.includes(item.pagePath))
          }
          
          // 应用修改
          if (modification.modifications) {
            config = config.map(item => {
              const mod = modification.modifications[item.pagePath]
              return mod ? { ...item, ...mod } : item
            })
          }
        }
        
        return config
      },

      initTabBar() {
        // 直接使用domainName生成tabbar配置
        this.tabList = this.generateTabConfig(this.domainName)
        this.updateCurrentPath()
      },

      // 安全区域计算
      calcSafeArea() {
        try {
          const { safeArea } = uni.getSystemInfoSync()
          this.safeAreaBottom = `${safeArea.bottom - safeArea.height}px`
          console.log(this.safeAreaBottom)
        } catch (e) {
          console.warn("安全区域获取失败", e)
        }
      },

      // 更新跳转路径
      updateSelected(path) {
        this.currentPath = path
      },

      switchTab(item) {
        // 更新当前路径
        this.currentPath = item.pagePath
        
        uni.switchTab({
          url: item.pagePath,
          success: () => {
            // 路由成功后更新当前路径
            this.updateCurrentPath()
          },
          fail: err => {
            console.error("跳转失败:", err)
            // 降级方案
            uni.redirectTo({ 
              url: item.pagePath,
              success: () => {
                this.updateCurrentPath()
              }
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
  .tabbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    display: flex;
    background: #fff;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.06);
    /* 安全区域适配 */
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);
  }

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 22rpx;
    transition: all 0.2s;
  }

  .tab-item image {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 6rpx;
  }
  .active {
    color: #007aff;
    transform: translateY(-6rpx);
  }

  /* 小红点样式 */
  .badge {
    position: absolute;
    top: 6rpx;
    right: 30rpx;
    min-width: 36rpx;
    height: 36rpx;
    padding: 0 8rpx;
    background: #ff5b5b;
    border-radius: 100rpx;
    color: white;
    font-size: 20rpx;
    line-height: 36rpx;
    text-align: center;
  }
</style>
