<!--
 * @Description: 2025安全生产月线上知识竞赛
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-06-17 09:32:09
 * @LastEditTime: 2025-07-07 08:56:52
-->

<template>
  <view class="normal-login-container">
    <!-- <view class="title-header">
      <view class="title">2025年</view>
      <view class="title">安全生产月线上知识竞赛</view>
    </view> -->
    <view class="logincon">
      <view class="login-form-container">
        <view class="login-form-content">
          <uni-forms ref="registerFormRef" :rules="registerFormRule" :modelValue="registerForm">
            <uni-forms-item name="userName">
              <uni-easyinput v-model="registerForm.userName" placeholder="请输入姓名" />
            </uni-forms-item>
            <uni-forms-item name="mobile">
              <uni-easyinput
                maxlength="11"
                v-model="registerForm.mobile"
                placeholder="请输入手机号"
              />
              <!-- <view class="get-code-text">获取验证码</view> -->
            </uni-forms-item>
            <!-- <uni-forms-item name="code">
              <uni-easyinput placeholder="请输入验证码" />
            </uni-forms-item> -->
            <uni-forms-item name="remark">
              <uni-easyinput v-model="registerForm.remark" placeholder="请输入地址" />
            </uni-forms-item>
            <view class="mobile-tip"> *手机号码请仔细填写，以免中奖后我们联系不到您! </view>
            <view class="action-btn">
              <button class="login-btn cu-btn block bg-blue lg round" @click="handleRegister">
                提交
              </button>
            </view>
          </uni-forms>
        </view>
      </view>
    </view>
    <yangr-msg
      v-if="yangrMsgShow"
      :title="title"
      :info="info"
      :type="type"
      :btn="btn"
      @yangrMsgEvent="confirmRegister"
    ></yangr-msg>
  </view>
</template>

<script>
  import yangrMsg from "@/components/yangr-msg/yangr-msg.vue"
  import { zhzgRegister } from "@/api/system/login.js"
  import { sendCode } from "@/api/competition/index"
  import { mapGetters } from "vuex"

  export default {
    components: {
      yangrMsg
    },
    data() {
      return {
        registerForm: {
          userName: "",
          mobile: "",
          remark: ""
        },
        canCode: false,
        codeMsg: "获取验证码",
        interval: null,
        activeTab: 0,
        countryList: [],
        registerFormRule: {
          userName: {
            rules: [
              {
                required: true,
                errorMessage: "姓名不能为空"
              }
            ]
          },
          mobile: {
            rules: [
              {
                required: true,
                errorMessage: "手机号不能为空"
              },
              {
                validateFunction: function (rule, value, data, callback) {
                  if (!/^[1][3,4,5,7,8,9][0-9]{9}$/.test(value)) {
                    callback("手机号格式不正确，请重新填写")
                  }
                }
              }
            ]
          },
          remark: {
            rules: [
              {
                required: true,
                errorMessage: "地址不能为空"
              }
            ]
          }
        },
        title: "",
        info: "",
        type: "",
        yangrMsgShow: false,
        btn: "",
        baseId: "",
        arrangeId: "",
        userType: null,
        deptList: [],
        mobileInputTimer: null
      }
    },
    mounted() {
      if (this.$route.query.baseId) {
        this.baseId = this.$route.query.baseId
      }
      if (this.$route.query.arrangeId) {
        this.arrangeId = this.$route.query.arrangeId
      }

      this.$nextTick(() => {
        if (this.$refs.registerFormRef && this.$refs.registerFormRef.setRules) {
          this.$refs.registerFormRef.setRules(this.registerFormRule)
        }
      })
    },
    computed: {
      ...mapGetters({
        domainInfo: "domainInfo",
        domainName: "domainName"
      })
    },
    methods: {
      // 判断验证码是否可点击
      // checkCode(val) {
      //   if (val && isContactWay(val)) {
      //     this.canCode = true
      //   } else {
      //     this.canCode = false
      //   }
      // },
      // 获取验证码
      // async getCode() {
      //   if (this.canCode) {
      //     let queryData = {
      //       mobile: this.registerForm.mobile,
      //       scene: 1
      //     }
      //     clearInterval(this.interval)
      //     this.canCode = false
      //     this.sec = 60
      //     this.codeMsg = this.sec + "s后重试"
      //     await sendCode(queryData)
      //     this.$modal.msgSuccess("验证码发送成功！")
      //     this.interval = setInterval(() => {
      //       this.sec -= 1
      //       this.codeMsg = this.sec + "s后重试"
      //       if (this.sec === 0) {
      //         this.codeMsg = "重新获取"
      //         this.canCode = true
      //         clearInterval(this.interval)
      //       }
      //     }, 1000)
      //   }
      // },
      // 注册
      handleRegister() {
        // 活动已结束，显示提示弹窗
        this.title = "提示"
        this.info = "活动已结束"
        this.type = "info"
        this.btn = "确定"
        this.yangrMsgShow = true
        
        // 注释掉原来的登录逻辑
        // this.$refs["registerFormRef"].validate().then(async () => {
        //   // 确保dept是id值
        //   const params = {
        //     ...this.registerForm,
        //     dept: 112
        //   }
        //   console.log("提交的表单数据:", params)

        //   const res = await zhzgRegister(params)
        //   if (res.code === 200) {
        //     this.$store
        //       .dispatch("Login", {
        //         username: this.registerForm.mobile,
        //         password: "123456"
        //       })
        //       .then(async () => {
        //         await this.loginSuccess()
        //       })
        //   }
        // })
      },
      // 登录成功后，处理函数
      async loginSuccess() {
        try {
          // 设置用户信息
          await this.$store.dispatch("GetInfo")

          let url = `/pages/mine/exam/prepare/index?baseId=${this.baseId}&arrangeId=${this.arrangeId}&showNavBar=false`
          uni.navigateTo({
            url: url
          })
        } catch (error) {
          console.error("检查考试状态时出错:", error)
          let url = `/pages/mine/exam/prepare/index?baseId=${this.baseId}&arrangeId=${this.arrangeId}&showNavBar=false`
          uni.navigateTo({
            url: url
          })
        }
      },
      confirmRegister() {
        // 活动已结束，只关闭弹窗
        this.yangrMsgShow = false
        
        // 注释掉原来的登录逻辑
        // this.$store
        //   .dispatch("Login", {
        //     username: this.registerForm.mobile,
        //     password: "123456"
        //   })
        //   .then(async () => {
        //     await this.loginSuccess()
        //   })
      }
    }
  }
</script>

<style lang="scss">
  ::v-deep .uni-forms-item {
    margin-bottom: 30rpx;
  }
  ::v-deep .uni-forms-item__label {
    display: none;
  }

  ::v-deep .uni-easyinput__content {
    border-radius: 10rpx;
    height: 70rpx;
    .uni-easyinput__placeholder-class,
    .uni-input-input {
      font-size: 32rpx !important;
      padding-left: 28rpx !important;
    }
  }
  ::v-deep .fixed-msg-info {
    font-size: 58rpx;
    font-weight: bold;
    color: #333;
  }
  page {
    background-color: #f2f2f2;
  }
  .active {
    color: #888 !important;
  }
  .normal-login-container {
    background: url("https://training-voc.obs.cn-north-4.myhuaweicloud.com/jxyj/login-bg.jpg")
      no-repeat center center;
    background-size: 100% 100%;
    width: 100%;
    height: 100vh;
    overflow: hidden;

    .title-header {
      background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
      padding: 80rpx 60rpx 60rpx 60rpx;
      border-radius: 0 0 50rpx 50rpx;
      position: relative;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: -50%;
        right: -20%;
        width: 200rpx;
        height: 200rpx;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
      }

      &::after {
        content: "";
        position: absolute;
        top: 20%;
        left: -10%;
        width: 150rpx;
        height: 150rpx;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 50%;
      }

      .title {
        text-align: center;
        color: #ffffff;
        font-size: 48rpx;
        font-weight: 700;
        line-height: 1.3;
        position: relative;
        z-index: 1;

        &:first-child {
          color: #2c5aa0;
          margin-bottom: 10rpx;
        }
      }
    }

    .logincon {
      padding: 75% 60rpx 0 60rpx;
      display: flex;
      justify-content: center;
      flex-direction: column;
      width: 100%;
      align-items: center;

      .login-form-container {
        width: 100%;
        padding: 0 20rpx;
        .login-form-content {
          padding: 20rpx 0;

          .code-container {
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .login-form-content {
      text-align: center;
      margin: 10px auto 10px auto;
      width: 80%;

      .login-btn {
        background-color: #3a89e6;
        margin-top: 40rpx;
        height: 45px;
      }

      .xieyi {
        color: #333;
        font-size: 14px;
      }

      .login-code {
        height: 38px;
        float: right;

        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }
    }
  }

  .xieyi {
    font-size: 12px;
  }
  .action-btn {
    button {
      font-size: 16px;
    }
  }

  .mobile-tip {
    color: #ff0000;
    font-size: 24rpx;
    text-align: left;
    margin-bottom: 30rpx;
    line-height: 1.4;
  }

  .get-code-text {
    color: #2c5aa0;
    font-size: 28rpx;
    text-align: right;
    margin-top: 20rpx;
    cursor: pointer;
  }
</style>
