{"name": "app-learning-h5", "version": "0.1.0", "private": true, "scripts": {"dev": "pnpm run dev:h5", "build:hwh5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --mode hwproduction && node huawei-obs-upload.js eduapp eduapp1", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --mode production", "build:xdh5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --mode xdproduction && node huawei-obs-upload.js eduxdapp", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build --mode hwproduction", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-jd": "cross-env NODE_ENV=production UNI_PLATFORM=mp-jd vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-lark": "cross-env NODE_ENV=production UNI_PLATFORM=mp-lark vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:mp-xhs": "cross-env NODE_ENV=production UNI_PLATFORM=mp-xhs vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env HTTPS=true NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-jd": "cross-env NODE_ENV=development UNI_PLATFORM=mp-jd vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-lark": "cross-env NODE_ENV=development UNI_PLATFORM=mp-lark vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:mp-xhs": "cross-env NODE_ENV=development UNI_PLATFORM=mp-xhs vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2-3080420230530001", "@dcloudio/uni-app-plus": "^2.0.2-3080420230530001", "@dcloudio/uni-h5": "^2.0.2-3080420230530001", "@dcloudio/uni-i18n": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-360": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-alipay": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-baidu": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-jd": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-kuaishou": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-lark": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-qq": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-toutiao": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-vue": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-weixin": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-xhs": "^2.0.2-3080420230530001", "@dcloudio/uni-quickapp-native": "^2.0.2-3080420230530001", "@dcloudio/uni-quickapp-webview": "^2.0.2-3080420230530001", "@dcloudio/uni-stacktracey": "^2.0.2-3080420230530001", "@dcloudio/uni-stat": "^2.0.2-3080420230530001", "@dcloudio/uni-ui": "^1.4.28", "@iconify-json/material-symbols": "^1.2.10", "@js-preview/docx": "^1.6.4", "@js-preview/excel": "^1.7.14", "@js-preview/pdf": "^2.0.10", "@unocss/preset-attributify": "0.39.3", "@unocss/preset-icons": "0.39.3", "@unocss/preset-uno": "0.39.3", "@unocss/webpack": "0.39.3", "@vue/shared": "^3.0.0", "buffer": "^6.0.3", "core-js": "^3.8.3", "dayjs": "^1.11.7", "flyio": "^0.6.2", "js-base64": "^3.7.5", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "uni-read-pages": "^1.0.5", "uni-simple-router": "^2.0.7", "unocss": "0.39.3", "util": "^0.12.5", "uview-ui": "^2.0.36", "video.js": "^8.6.0", "videojs-landscape-fullscreen": "^11.1111.0", "vue": ">= 2.6.14 < 2.7", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-cli-i18n": "^2.0.2-3080420230530001", "@dcloudio/uni-cli-shared": "^2.0.2-3080420230530001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-migration": "^2.0.2-3080420230530001", "@dcloudio/uni-template-compiler": "^2.0.2-3080420230530001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2-3080420230530001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-3080420230530001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2-3080420230530001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-3080420230530001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-3080420230530001", "@vue/babel-helper-vue-transform-on": "^1.1.4", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "huawei-obs-plugin": "^1.0.2", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0", "sass": "^1.54.9", "sass-loader": "^7.3.1"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}