/*
 * @Description: 文件类型判断工具函数
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-07-23 16:00:00
 * @LastEditTime: 2025-07-23 09:57:53
 */

/**
 * 判断是否为docx文件
 * @param {string} url 文件URL
 * @returns {boolean}
 */
export function isDocxFile(url) {
  if (!url) return false
  const lowerUrl = url.toLowerCase()
  return lowerUrl.includes(".docx")
}

/**
 * 判断是否为Excel文件
 * @param {string} url 文件URL
 * @returns {boolean}
 */
export function isExcelFile(url) {
  if (!url) return false
  const lowerUrl = url.toLowerCase()
  return lowerUrl.includes(".xlsx") || lowerUrl.includes(".xls")
}

/**
 * 判断是否为PDF文件
 * @param {string} url 文件URL
 * @returns {boolean}
 */
export function isPdfFile(url) {
  if (!url) return false
  const lowerUrl = url.toLowerCase()
  return lowerUrl.includes(".pdf")
}

/**
 * 判断是否为PPTX文件
 * @param {string} url 文件URL
 * @returns {boolean}
 */
export function isPptxFile(url) {
  if (!url) return false
  const lowerUrl = url.toLowerCase()
  return lowerUrl.includes(".pptx")
}

/**
 * 判断是否为Office文件（doc、xls、ppt等旧格式）
 * @param {string} url 文件URL
 * @returns {boolean}
 */
export function isOfficeFile(url) {
  if (!url) return false
  const lowerUrl = url.toLowerCase()
  return lowerUrl.includes(".doc") || lowerUrl.includes(".xls") || lowerUrl.includes(".ppt")
}

/**
 * 判断是否为图片文件
 * @param {string} url 文件URL
 * @returns {boolean}
 */
export function isImgPath(url) {
  if (!url) return false
  const lowerUrl = url.toLowerCase()
  return (
    lowerUrl.includes(".jpg") ||
    lowerUrl.includes(".jpeg") ||
    lowerUrl.includes(".png") ||
    lowerUrl.includes(".gif") ||
    lowerUrl.includes(".webp") ||
    lowerUrl.includes(".bmp")
  )
}

/**
 * 判断是否为视频文件
 * @param {string} url 文件URL
 * @returns {boolean}
 */
export function isVideoFile(url) {
  if (!url) return false
  const lowerUrl = url.toLowerCase()
  return (
    lowerUrl.includes(".mp4") ||
    lowerUrl.includes(".avi") ||
    lowerUrl.includes(".mov") ||
    lowerUrl.includes(".wmv") ||
    lowerUrl.includes(".flv") ||
    lowerUrl.includes(".mkv")
  )
}
