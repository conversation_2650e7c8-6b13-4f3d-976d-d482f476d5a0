<!--
 * @Description: 首页
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-12 08:48:30
 * @LastEditTime: 2025-07-18 08:44:34
-->
<template>
  <view class="content">
    <RecentStudyPopup />
    <uni-popup ref="videoPopup" type="center">
      <view class="relative bg-white rounded-lg w-85vw p-4">
        <view class="flex justify-between items-center mb-2">
          <text class="text-16px font-medium text-gray-800">{{ currentVideo.title }}</text>
          <view
            class="cursor-pointer w-8 h-8 flex items-center justify-center"
            @click="closeVideoPopup"
          >
            <text class="i-material-symbols-close text-20px text-gray-600"></text>
          </view>
        </view>
        <view class="w-full aspect-video bg-black">
          <video
            :src="currentVideo.url"
            class="w-full h-full"
            :controls="true"
            :show-center-play-btn="true"
            :enable-progress-gesture="true"
            :show-fullscreen-btn="true"
          ></video>
        </view>
      </view>
    </uni-popup>
    <view class="bg-#fff">
      <GlobalHeader />
      <!-- <view
        class="flex justify-between items-center m-2 bg-white p-2 text-14px shadow-lg rounded-lg border border-#f2f2f2 border-solid"
      >
        <view class="flex gap-2 items-center">
          <view class="bg-orange-500 text-white px-2 py-1 rounded">最近学习</view>
          <view>众多视频学习内容，快去学习吧</view>
        </view>
        <view class="text-orange-500" @click="jumpToLearn">去学习 &gt;</view>
      </view> -->
    </view>

    <ContentCompSwiper
      :list="bannerList"
      class="shadow-lg rounded-lg overflow-hidden border border-#f2f2f2 border-solid bg-#fff w-full !mt-2"
    />
    <ContentCompGridArea
      :list="dataList"
      class="shadow-lg rounded-lg m-2 p-2 border border-#f2f2f2 border-solid bg-#fff"
    />
    <view class="bg-#fff shadow-lg rounded-lg m-2 p-2 border border-#f2f2f2 border-solid">
      <view class="flex justify-between items-center mb-2 px-15px">
        <view class="flex items-center gap-2">
          <image class="!w-20px !h-20px" src="/static/images/heart.png"></image>
          <span>推荐课程</span>
        </view>
        <view class="text-16px text-#5b5b5b flex items-center" @click="switchToCourse">
          <image
            class="!w-26px !h-26px"
            src="https://training-voc.obs.cn-north-4.myhuaweicloud.com/home_more.png"
          ></image>
        </view>
      </view>
      <view class="flex flex-wrap gap-2 ml-2">
        <view
          v-for="(item, index) in courseList"
          :key="item.courseId"
          @click="jumpToCourse(item)"
          class="w-[48%] mb-1"
        >
          <view class="border border-#e3e3e3 border-solid rounded-md overflow-hidden">
            <view class="relative w-full pb-[56.25%]">
              <image
                class="absolute inset-0 w-full h-full object-cover"
                :src="item.courseImage"
                mode="aspectFill"
              />
            </view>
            <view class="p-2 text-14px text-#5b5b5b truncate">{{ item.courseName }}</view>
          </view>
        </view>
      </view>
    </view>
    <view
      class="bg-#fff shadow-lg rounded-lg m-2 p-2 border border-#f2f2f2 border-solid"
      v-if="videoList.length"
    >
      <view class="flex justify-between items-center mb-2 px-15px">
        <view class="flex items-center gap-2">
          <view class="w-4px h-20px bg-blue-500 rounded"></view>
          <span>公益宣传片</span>
        </view>
      </view>
      <view class="flex flex-wrap gap-2 ml-2">
        <view
          v-for="(item, index) in videoList"
          :key="item.psaId"
          @click="playVideo(item)"
          class="w-[48%] mb-1"
        >
          <view class="border border-#e3e3e3 border-solid rounded-md overflow-hidden">
            <view class="relative w-full pb-[56.25%]">
              <image
                class="absolute inset-0 w-full h-full object-cover"
                :src="item.cover"
                mode="aspectFill"
              />
            </view>
            <view class="p-2 text-14px text-#5b5b5b truncate">{{ item.title }}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 自定义tabbar -->
    <CustomTabbar />
  </view>
</template>

<script>
  import ContentCompSwiper from "@/components/contentComp/contentCompSwiper.vue"
import ContentCompGridArea from "@/components/contentComp/contentCompGridArea.vue"
import { hotCourseList } from "@/api/course"
import { listVideo } from "@/api/trailer/index.js"
import GlobalHeader from "@/components/GlobalHeader/index.vue"
import { carousellist } from "@/api/devops/carousel.js"
import RecentStudyPopup from "@/components/RecentStudyPopup/index.vue"
import CustomTabbar from "@/components/custom-tab-bar/custom-tab-bar.vue"

  export default {
    components: {
      ContentCompSwiper,
      ContentCompGridArea,
      GlobalHeader,
      RecentStudyPopup,
      CustomTabbar
    },

    onLoad() {
      this.fetchCourseList()
      this.fetchVideoList()
      // this.getBannerList()
    },

    data() {
      return {
        courseList: [],
        videoList: [],
        actionBall: [],
        bannerList: [
          {
            img: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/home_swiper1.jpg"
          },
          {
            img: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/home_swiper2.jpg"
          },
          {
            img: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/home_swiper3.jpg"
          }
        ],
        currentVideo: {},
        dataList: [
          {
            id: 12,
            name: "资料中心",
            url: "/pages/home/<USER>/index?from=" + encodeURIComponent("/pages/index"),
            img: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/home_resource.png",
            bgc: "60, 202, 119"
          },
          {
            id: 72,
            name: "培训项目",
            url: "/pages/learn/index",
            img: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/home_task.png",
            bgc: "246, 129, 37"
          },
          {
            id: 73,
            name: "新闻资讯",
            url: "/pages/home/<USER>/index",
            img: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/home_news.png",
            bgc: "138, 182, 247"
          },
          {
            id: 75,
            name: "考试",
            url: "/pages/mine/exam/index?backPath=/pages/index",
            img: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/home_exam.png",
            bgc: "244, 165, 64"
          }
        ]
      }
    },

    methods: {
      // 获取轮播图数据
      async getBannerList() {
        let queryData = {
          pageNum: 1,
          pageSize: 10,
          carouselId: 2030
        }
        const { rows } = await carousellist(queryData)
        this.bannerList = rows || []
      },
      async fetchCourseList() {
        const { data } = await hotCourseList()
        this.courseList = data?.slice(0, 6) || []
      },

      async fetchVideoList() {
        const { rows } = await listVideo()
        this.videoList = rows?.slice(0, 6) || []
      },

      jumpTo() {
        uni.navigateTo({ url: "/pages/home/<USER>/index" })
      },

      jumpToCourse(item) {
        if (item.hasTask) {
          this.jumpToLearn()
        } else {
          uni.navigateTo({
            url: "/pages/course/detail/index?id=" + item.courseId
          })
        }
      },

      jumpToLearn() {
        uni.switchTab({ url: "/pages/learn/index" })
      },
      switchToCourse() {
        uni.switchTab({ url: "/pages/course/index" })
      },
      switchToVideo() {
        uni.navigateTo({ url: "/pages/video/index" })
      },
      playVideo(item) {
        this.currentVideo = item
        this.$refs.videoPopup.open()
      },

      closeVideoPopup() {
        this.$refs.videoPopup.close()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .content {
    width: 100%;
    min-height: calc(100vh - var(--window-bottom));
    background: #f8f8f8;
    overflow: hidden;
    padding-bottom: calc(100rpx + env(safe-area-inset-bottom) + 20rpx);
  }
</style>
