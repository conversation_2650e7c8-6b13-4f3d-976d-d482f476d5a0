<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-10 13:36:33
 * @LastEditTime: 2025-07-17 15:36:21
-->
<template>
  <div class="flex items-center justify-between h-16 bg-[#1e91fc] pl-4">
    <div class="flex items-center">
      <image
        class="w-50px"
        :src="
          (domainInfo && domainInfo.tenantIcon) || 'https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/beckwell.png'
        "
        mode="widthFix"
      ></image>
    </div>

    <div class="flex-1 mx-4">
      <div class="relative flex items-center">
        <div class="absolute left-3 text-white">
          <div class="i-material-symbols-search text-20px"></div>
        </div>
        <input
          type="text"
          v-model="searchKeyword"
          :placeholder="searchPlaceholder"
          class="w-full h-10 pl-10 pr-10 rounded-full bg-[#61b2fd] border-0 text-white focus:outline-none search-input"
          confirm-type="search"
          @confirm="handleSearch"
        />
        <div 
          v-if="searchKeyword"
          class="absolute right-3 text-white cursor-pointer"
          @click="clearSearch"
        >
          <div class="i-material-symbols-close text-20px"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"

export default {
  name: "GlobalHeader",

  props: {
    // 默认搜索关键词
    defaultKeyword: {
      type: String,
      default: ''
    },
    // 搜索占位符
    searchPlaceholder: {
      type: String,
      default: '搜索课程'
    }
  },

  data() {
    return {
      searchKeyword: this.defaultKeyword || ''
    }
  },

  watch: {
    // 监听默认关键词的变化
    defaultKeyword: {
      handler(newVal) {
        if (newVal !== this.searchKeyword) {
          this.searchKeyword = newVal
        }
      },
      immediate: true
    }
  },

  computed: {
    ...mapGetters({
      domainInfo: "domainInfo"
    }),
    // 判断是否在课程页面
    isCoursePage() {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      return currentPage && currentPage.route === 'pages/course/index'
    }
  },

  methods: {
    handleSearch() {
      const keyword = this.searchKeyword.trim()

      // 优先触发search事件，让父组件处理
      this.$emit('search', keyword)

      // 如果父组件监听了search事件，就不执行默认行为
      // 通过检查$listeners来判断是否有父组件监听search事件
      if (this.$listeners.search) {
        return
      }

      // 默认行为：跳转到课程页面搜索
      if (!keyword) return

      if (this.isCoursePage) {
        // 如果在课程页面，直接触发父组件的搜索方法
        this.$emit('on-search', keyword)
      } else {
        // 如果不在课程页面，使用reLaunch跳转，确保每次都会触发onLoad
        uni.reLaunch({
          url: `/pages/course/index?keyword=${encodeURIComponent(keyword)}`
        })
      }
    },

    clearSearch() {
      this.searchKeyword = ''
      // 触发清空搜索事件
      this.$emit('search', '')

      // 如果父组件监听了search事件，就不执行默认行为
      if (this.$listeners.search) {
        return
      }

      if (this.isCoursePage) {
        // 如果在课程页面，触发搜索方法重新获取全部数据
        this.$emit('on-search', '')
      }
    }
  }
}
</script>

<style scoped>
.search-input {
  color: white;
}
.search-input::placeholder {
  color: white !important;
  opacity: 1;
}
/* 兼容 Firefox */
.search-input::-moz-placeholder {
  color: white !important;
  opacity: 1;
}
/* 兼容 IE */
.search-input:-ms-input-placeholder {
  color: white !important;
}
/* 兼容 Chrome/Safari */
.search-input::-webkit-input-placeholder {
  color: white !important;
}
.uni-input-placeholder {
  color: white !important;
}
</style>
